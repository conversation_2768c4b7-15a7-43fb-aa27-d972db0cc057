# THE OVERMIND PROTOCOL - Production Monitoring Configuration
# Comprehensive monitoring, alerting, and observability setup

# =============================================================================
# PROMETHEUS CONFIGURATION
# =============================================================================
prometheus:
  global:
    scrape_interval: 15s
    evaluation_interval: 15s
  
  rule_files:
    - "alert_rules.yml"
  
  scrape_configs:
    # THE OVERMIND PROTOCOL main application
    - job_name: 'overmind-trading'
      static_configs:
        - targets: ['localhost:8080']
      scrape_interval: 5s
      metrics_path: '/metrics'
    
    # AI Brain monitoring
    - job_name: 'overmind-ai-brain'
      static_configs:
        - targets: ['localhost:8000']
      scrape_interval: 10s
    
    # DragonflyDB monitoring
    - job_name: 'dragonfly'
      static_configs:
        - targets: ['localhost:6379']
      scrape_interval: 10s
    
    # System metrics
    - job_name: 'node-exporter'
      static_configs:
        - targets: ['localhost:9100']
      scrape_interval: 15s

  alerting:
    alertmanagers:
      - static_configs:
          - targets: ['localhost:9093']

# =============================================================================
# ALERT RULES
# =============================================================================
alert_rules:
  groups:
    - name: trading_alerts
      rules:
        # Critical trading alerts
        - alert: HighDailyLoss
          expr: daily_loss_sol > 5.0
          for: 0m
          labels:
            severity: critical
          annotations:
            summary: "Daily loss limit exceeded"
            description: "Daily loss of {{ $value }} SOL exceeds limit of 5.0 SOL"
        
        - alert: MaxPositionSizeExceeded
          expr: max_position_size_sol > 10.0
          for: 0m
          labels:
            severity: critical
          annotations:
            summary: "Position size limit exceeded"
            description: "Position size of {{ $value }} SOL exceeds limit of 10.0 SOL"
        
        - alert: HighExecutionLatency
          expr: execution_latency_ms > 10
          for: 30s
          labels:
            severity: warning
          annotations:
            summary: "High execution latency detected"
            description: "Execution latency of {{ $value }}ms exceeds target of 10ms"
        
        - alert: AIAnalysisTimeout
          expr: ai_analysis_latency_ms > 5000
          for: 10s
          labels:
            severity: warning
          annotations:
            summary: "AI analysis taking too long"
            description: "AI analysis latency of {{ $value }}ms exceeds target of 5000ms"
        
        - alert: TradingSystemDown
          expr: up{job="overmind-trading"} == 0
          for: 30s
          labels:
            severity: critical
          annotations:
            summary: "Trading system is down"
            description: "THE OVERMIND PROTOCOL trading system is not responding"
        
        - alert: HighErrorRate
          expr: rate(trading_errors_total[5m]) > 0.1
          for: 2m
          labels:
            severity: warning
          annotations:
            summary: "High error rate detected"
            description: "Error rate of {{ $value }} errors/sec is above threshold"
        
        - alert: LowProfitability
          expr: hourly_profit_percentage < -2.0
          for: 1h
          labels:
            severity: warning
          annotations:
            summary: "Low profitability detected"
            description: "Hourly profit of {{ $value }}% is below -2%"

    - name: infrastructure_alerts
      rules:
        - alert: HighCPUUsage
          expr: cpu_usage_percentage > 80
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "High CPU usage"
            description: "CPU usage of {{ $value }}% is above 80%"
        
        - alert: HighMemoryUsage
          expr: memory_usage_percentage > 85
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "High memory usage"
            description: "Memory usage of {{ $value }}% is above 85%"
        
        - alert: DragonflyDBDown
          expr: up{job="dragonfly"} == 0
          for: 30s
          labels:
            severity: critical
          annotations:
            summary: "DragonflyDB is down"
            description: "DragonflyDB is not responding"
        
        - alert: ChromaDBDown
          expr: up{job="overmind-ai-brain"} == 0
          for: 30s
          labels:
            severity: critical
          annotations:
            summary: "AI Brain / Chroma is down"
            description: "AI Brain or Chroma database is not responding"

# =============================================================================
# ALERTMANAGER CONFIGURATION
# =============================================================================
alertmanager:
  global:
    smtp_smarthost: 'localhost:587'
    smtp_from: '<EMAIL>'
  
  route:
    group_by: ['alertname']
    group_wait: 10s
    group_interval: 10s
    repeat_interval: 1h
    receiver: 'web.hook'
    routes:
      - match:
          severity: critical
        receiver: 'critical-alerts'
      - match:
          severity: warning
        receiver: 'warning-alerts'
  
  receivers:
    - name: 'web.hook'
      webhook_configs:
        - url: 'http://localhost:5001/'
    
    - name: 'critical-alerts'
      discord_configs:
        - webhook_url: '${DISCORD_WEBHOOK_URL}'
          title: '🚨 CRITICAL ALERT - THE OVERMIND PROTOCOL'
          message: |
            **Alert:** {{ .GroupLabels.alertname }}
            **Severity:** {{ .CommonLabels.severity }}
            **Description:** {{ .CommonAnnotations.description }}
            **Time:** {{ .CommonAnnotations.timestamp }}
      
      telegram_configs:
        - bot_token: '${TELEGRAM_BOT_TOKEN}'
          chat_id: '${TELEGRAM_CHAT_ID}'
          message: |
            🚨 *CRITICAL ALERT*
            
            *Alert:* {{ .GroupLabels.alertname }}
            *Severity:* {{ .CommonLabels.severity }}
            *Description:* {{ .CommonAnnotations.description }}
            *Time:* {{ .CommonAnnotations.timestamp }}
    
    - name: 'warning-alerts'
      discord_configs:
        - webhook_url: '${DISCORD_WEBHOOK_URL}'
          title: '⚠️ WARNING - THE OVERMIND PROTOCOL'
          message: |
            **Alert:** {{ .GroupLabels.alertname }}
            **Severity:** {{ .CommonLabels.severity }}
            **Description:** {{ .CommonAnnotations.description }}
            **Time:** {{ .CommonAnnotations.timestamp }}

# =============================================================================
# GRAFANA DASHBOARDS
# =============================================================================
grafana:
  dashboards:
    - name: "THE OVERMIND PROTOCOL - Trading Overview"
      panels:
        - title: "Real-time P&L"
          type: "stat"
          targets:
            - expr: "current_pnl_sol"
        
        - title: "Daily Profit/Loss"
          type: "graph"
          targets:
            - expr: "daily_pnl_sol"
        
        - title: "Execution Latency"
          type: "graph"
          targets:
            - expr: "execution_latency_ms"
        
        - title: "AI Analysis Performance"
          type: "graph"
          targets:
            - expr: "ai_analysis_latency_ms"
        
        - title: "Active Positions"
          type: "table"
          targets:
            - expr: "active_positions"
        
        - title: "Success Rate"
          type: "stat"
          targets:
            - expr: "trade_success_rate_percentage"
    
    - name: "THE OVERMIND PROTOCOL - System Health"
      panels:
        - title: "CPU Usage"
          type: "graph"
          targets:
            - expr: "cpu_usage_percentage"
        
        - title: "Memory Usage"
          type: "graph"
          targets:
            - expr: "memory_usage_percentage"
        
        - title: "Network Latency"
          type: "graph"
          targets:
            - expr: "network_latency_ms"
        
        - title: "Error Rate"
          type: "graph"
          targets:
            - expr: "rate(trading_errors_total[5m])"

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
logging:
  level: "info"
  format: "json"
  outputs:
    - type: "file"
      path: "/var/log/overmind/trading.log"
      max_size: "100MB"
      max_files: 10
    
    - type: "console"
      format: "human"
    
    - type: "syslog"
      facility: "local0"
  
  structured_fields:
    - "timestamp"
    - "level"
    - "component"
    - "trade_id"
    - "strategy"
    - "profit_loss"
    - "latency_ms"

# =============================================================================
# HEALTH CHECKS
# =============================================================================
health_checks:
  endpoints:
    - name: "trading_api"
      url: "http://localhost:8080/health"
      interval: "30s"
      timeout: "5s"
    
    - name: "ai_brain"
      url: "http://localhost:8000/health"
      interval: "30s"
      timeout: "5s"
    
    - name: "dragonfly"
      url: "redis://localhost:6379"
      interval: "30s"
      timeout: "5s"
      command: "PING"
  
  failure_thresholds:
    consecutive_failures: 3
    failure_rate_window: "5m"
    max_failure_rate: 0.1
