# THE OVERMIND PROTOCOL - Production Environment Template
# Copy this file to .env and fill in your actual API keys
# NEVER commit actual API keys to version control!

# =============================================================================
# TRADING CONFIGURATION
# =============================================================================
SNIPER_TRADING_MODE=live
OVERMIND_AI_MODE=enabled
OVERMIND_ENVIRONMENT=production

# =============================================================================
# SOLANA NETWORK CONFIGURATION
# =============================================================================
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_WS_URL=wss://api.mainnet-beta.solana.com
SOLANA_NETWORK=mainnet-beta

# Backup RPC endpoints for failover
SOLANA_RPC_BACKUP_1=https://solana-api.projectserum.com
SOLANA_RPC_BACKUP_2=https://rpc.ankr.com/solana
SOLANA_RPC_BACKUP_3=https://solana.blockdaemon.com

# =============================================================================
# HELIUS API CONFIGURATION (REQUIRED FOR PRODUCTION)
# =============================================================================
HELIUS_API_KEY=your_helius_api_key_here
HELIUS_RPC_URL=https://mainnet.helius-rpc.com/?api-key=your_helius_api_key_here
HELIUS_WEBSOCKET_URL=wss://mainnet.helius-rpc.com/?api-key=your_helius_api_key_here

# =============================================================================
# JITO V2 CONFIGURATION (REQUIRED FOR MEV)
# =============================================================================
JITO_API_KEY=your_jito_v2_api_key_here
JITO_BUNDLE_URL=https://mainnet.block-engine.jito.wtf/api/v1/bundles
JITO_TIP_ACCOUNT=96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5
JITO_VALIDATOR_URL=https://mainnet.block-engine.jito.wtf/api/v1/validators

# =============================================================================
# AI SERVICES CONFIGURATION
# =============================================================================
# DeepSeek V2 for advanced reasoning
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com

# Jina AI for embeddings and reranking
JINA_API_KEY=your_jina_api_key_here
JINA_BASE_URL=https://api.jina.ai

# TensorZero for AI optimization
TENSORZERO_API_KEY=your_tensorzero_api_key_here
TENSORZERO_BASE_URL=https://api.tensorzero.com

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# DragonflyDB (Redis-compatible)
DRAGONFLY_URL=redis://localhost:6379
DRAGONFLY_PASSWORD=your_secure_dragonfly_password

# Chroma Vector Database
CHROMA_URL=http://localhost:8000
CHROMA_API_KEY=your_chroma_api_key_here

# =============================================================================
# WALLET CONFIGURATION (CRITICAL SECURITY)
# =============================================================================
# Main trading wallet private key (KEEP SECURE!)
MAIN_WALLET_PRIVATE_KEY=your_main_wallet_private_key_here

# Backup wallets for different strategies
HFT_WALLET_PRIVATE_KEY=your_hft_wallet_private_key_here
ARBITRAGE_WALLET_PRIVATE_KEY=your_arbitrage_wallet_private_key_here
EXPERIMENTAL_WALLET_PRIVATE_KEY=your_experimental_wallet_private_key_here

# =============================================================================
# RISK MANAGEMENT CONFIGURATION
# =============================================================================
# Maximum daily loss (in SOL)
SNIPER_MAX_DAILY_LOSS=5.0

# Maximum position size (in SOL)
SNIPER_MAX_POSITION_SIZE=10.0

# Maximum total exposure (in SOL)
SNIPER_MAX_TOTAL_EXPOSURE=25.0

# Stop loss percentage
SNIPER_STOP_LOSS_PERCENTAGE=5.0

# Take profit percentage
SNIPER_TAKE_PROFIT_PERCENTAGE=15.0

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
# Maximum latency targets (in milliseconds)
MAX_EXECUTION_LATENCY_MS=10
MAX_AI_ANALYSIS_LATENCY_MS=5
MAX_PRICE_FETCH_LATENCY_MS=50

# Concurrency settings
MAX_CONCURRENT_TRADES=5
MAX_CONCURRENT_ANALYSIS=10

# =============================================================================
# MONITORING & ALERTING
# =============================================================================
# Prometheus metrics
PROMETHEUS_PORT=9090
METRICS_ENABLED=true

# Logging configuration
LOG_LEVEL=info
LOG_FILE_PATH=/var/log/overmind/trading.log

# Alert webhooks
DISCORD_WEBHOOK_URL=your_discord_webhook_url_here
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# API rate limiting
API_RATE_LIMIT_PER_MINUTE=1000

# Encryption settings
ENCRYPTION_KEY=your_32_character_encryption_key_here
SALT=your_16_character_salt_here

# =============================================================================
# ADVANCED MEV CONFIGURATION
# =============================================================================
# MEV strategy weights (0.0 to 1.0)
MEV_ARBITRAGE_WEIGHT=0.3
MEV_LIQUIDATION_WEIGHT=0.2
MEV_SANDWICH_WEIGHT=0.1
MEV_FRONTRUN_WEIGHT=0.2
MEV_BACKRUN_WEIGHT=0.2

# Priority fee configuration
PRIORITY_FEE_MULTIPLIER=2.0
MAX_PRIORITY_FEE_LAMPORTS=100000

# Slippage tolerance
MAX_SLIPPAGE_PERCENTAGE=1.0
PREFERRED_SLIPPAGE_PERCENTAGE=0.5

# =============================================================================
# DEX CONFIGURATION
# =============================================================================
# Supported DEXes (comma-separated)
SUPPORTED_DEXES=jupiter,raydium,orca,serum,meteora

# DEX-specific settings
JUPITER_API_URL=https://quote-api.jup.ag/v6
RAYDIUM_API_URL=https://api.raydium.io/v2
ORCA_API_URL=https://api.orca.so

# =============================================================================
# DEVELOPMENT & DEBUGGING
# =============================================================================
# Debug mode (set to false in production)
DEBUG_MODE=false

# Dry run mode (for testing without real trades)
DRY_RUN_MODE=false

# Paper trading mode (for safe testing)
PAPER_TRADING_MODE=false

# =============================================================================
# BACKUP & RECOVERY
# =============================================================================
# Backup configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=6
BACKUP_RETENTION_DAYS=30

# Recovery settings
AUTO_RECOVERY_ENABLED=true
RECOVERY_TIMEOUT_SECONDS=300
