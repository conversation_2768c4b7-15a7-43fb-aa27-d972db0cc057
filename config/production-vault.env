# THE OVERMIND PROTOCOL - OPERACJA 'VAULT' v2.0
# Production Infisical Configuration
# Project: 73c2f3cb-c922-4a46-a333-7b96fbc6301a

# =============================================================================
# INFISICAL PRODUCTION CONFIGURATION
# =============================================================================

# Infisical API Configuration
INFISICAL_API_URL=https://app.infisical.com/api
INFISICAL_PROJECT_ID=73c2f3cb-c922-4a46-a333-7b96fbc6301a
INFISICAL_ENVIRONMENT=production

# Service Token Authentication (Production)
INFISICAL_SERVICE_TOKEN=st.31baa38e-572d-4abc-8de6-83b1abca9cbf.97a3bb72ec1ab7c1002a187feaaa31d3.ccae3c429818d256c68d768c15f22e78

# Fallback Machine Identity (if service token fails)
INFISICAL_CLIENT_ID=
INFISICAL_CLIENT_SECRET=

# =============================================================================
# DRAGONFLYDB CLOUD CONFIGURATION
# =============================================================================

# DragonflyDB Cloud Network
DRAGONFLYDB_VPC_ID=vpc-05f61f843ed60555e
DRAGONFLYDB_CIDR=***********/16
DRAGONFLYDB_ACCOUNT_ID=************

# DragonflyDB Connection (will be configured after setup)
DRAGONFLYDB_URL=redis://dragonflydb.cloud:6379
DRAGONFLYDB_PASSWORD=
DRAGONFLYDB_DATABASE=0

# Cache Configuration
CACHE_TTL_SECONDS=300
CACHE_MAX_SIZE=10000
CACHE_COMPRESSION=true

# =============================================================================
# THE OVERMIND PROTOCOL PRODUCTION SETTINGS
# =============================================================================

# Trading Mode
SNIPER_TRADING_MODE=live
OVERMIND_AI_MODE=enabled
OVERMIND_ENVIRONMENT=production

# Performance Settings
RUST_LOG=info
OVERMIND_WORKERS=8
OVERMIND_MAX_CONNECTIONS=1000

# Security Settings
ENABLE_RATE_LIMITING=true
MAX_REQUESTS_PER_MINUTE=10000
ENABLE_IP_WHITELIST=true
ALLOWED_IPS=***********/16

# Monitoring
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090
ENABLE_METRICS=true
ENABLE_TRACING=true

# =============================================================================
# NETWORK SECURITY
# =============================================================================

# VPC Configuration
VPC_ID=vpc-05f61f843ed60555e
SUBNET_CIDR=***********/16
SECURITY_GROUP_INGRESS=22,80,443,6379,9090

# SSL/TLS Configuration
TLS_ENABLED=true
TLS_CERT_PATH=/etc/ssl/certs/overmind.crt
TLS_KEY_PATH=/etc/ssl/private/overmind.key

# =============================================================================
# USAGE INSTRUCTIONS
# =============================================================================

# To use this configuration:
# 1. Source this file: source config/production-vault.env
# 2. Start THE OVERMIND PROTOCOL: cargo run --profile contabo
# 3. Or use the secure startup script: ./start-overmind-secure.sh

# For DragonflyDB integration:
# 1. Configure DragonflyDB instance in VPC vpc-05f61f843ed60555e
# 2. Update DRAGONFLYDB_URL with actual endpoint
# 3. Set DRAGONFLYDB_PASSWORD from DragonflyDB dashboard

# Security Notes:
# - Service token provides direct access to Infisical
# - VPC isolation ensures secure communication
# - All secrets are encrypted in transit and at rest
# - Cache layer provides sub-millisecond secret access
