# THE OVERMIND PROTOCOL - AlertManager Configuration
# Alert routing and notification setup

global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'
  routes:
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 5s
      repeat_interval: 30m
    - match:
        severity: warning
      receiver: 'warning-alerts'
      repeat_interval: 2h

receivers:
  - name: 'web.hook'
    webhook_configs:
      - url: 'http://localhost:5001/'

  - name: 'critical-alerts'
    webhook_configs:
      - url: '${DISCORD_WEBHOOK_URL}'
        title: '🚨 CRITICAL ALERT - THE OVERMIND PROTOCOL'
        send_resolved: true
        http_config:
          bearer_token: '${DISCORD_TOKEN}'

  - name: 'warning-alerts'
    webhook_configs:
      - url: '${DISCORD_WEBHOOK_URL}'
        title: '⚠️ WARNING - THE OVERMIND PROTOCOL'
        send_resolved: true

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'dev', 'instance']
