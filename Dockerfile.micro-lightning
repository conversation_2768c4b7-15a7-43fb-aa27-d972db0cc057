# MICRO-LIGHTNING MONITOR DOCKERFILE
# Specialized container for OPERACJA MIKRO-BŁYSKAWICA monitoring and control

FROM rust:1.75-slim as builder

# Install system dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libpq-dev \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy Cargo files
COPY Cargo.toml Cargo.lock ./

# Copy source code
COPY src/ ./src/
COPY tests/ ./tests/
COPY examples/ ./examples/

# Build the micro-lightning monitor binary
RUN cargo build --release --bin micro-lightning-monitor

# Runtime stage
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    libpq5 \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN useradd -r -s /bin/false -m -d /app overmind

# Set working directory
WORKDIR /app

# Copy binary from builder
COPY --from=builder /app/target/release/micro-lightning-monitor /app/
COPY --from=builder /app/examples/micro_lightning_demo /app/

# Copy configuration files
COPY config/micro-lightning/ ./config/
COPY docs/MICRO_LIGHTNING_SYSTEM.md ./docs/

# Create necessary directories
RUN mkdir -p /app/logs /app/data /app/metrics && \
    chown -R overmind:overmind /app

# Switch to app user
USER overmind

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8081/health || exit 1

# Expose port
EXPOSE 8081

# Set environment variables
ENV RUST_LOG=info
ENV MICRO_LIGHTNING_CONFIG_PATH=/app/config
ENV MICRO_LIGHTNING_LOG_PATH=/app/logs
ENV MICRO_LIGHTNING_DATA_PATH=/app/data

# Start the micro-lightning monitor
CMD ["./micro-lightning-monitor"]
