use pqcrypto_mlkem::{keypair, encapsulate, decapsulate};
use aes_gcm_siv::{Aes256GcmSiv, KeyInit, Nonce};
use aes_gcm_siv::aead::{Aead, NewAead};
use rand::rngs::OsRng;
use anyhow::{Result, anyhow};

pub struct QuantumVault {
    kyber_pk: Vec<u8>,
    kyber_sk: Vec<u8>,
}

impl QuantumVault {
    pub fn new() -> Self {
        let (pk, sk) = keypair();
        Self { 
            kyber_pk: pk.to_vec(), 
            kyber_sk: sk.to_vec() 
        }
    }

    pub fn encrypt_secret(&self, plaintext: &[u8]) -> Result<(Vec<u8>, Vec<u8>)> {
        // ML-KEM dla post-quantum key exchange
        let (ciphertext, shared_secret) = encapsulate(&self.kyber_pk);
        
        // AES-GCM-SIV dla szyfrowania symetrycznego (odporne na błędy nonce)
        let key = aes_gcm_siv::Key::<Aes256GcmSiv>::from_slice(&shared_secret);
        let cipher = Aes256GcmSiv::new(key);
        let nonce = Nonce::from_slice(b"unique_nonce");
        
        let encrypted = cipher.encrypt(nonce, plaintext)
            .map_err(|e| anyhow!("Encryption failed: {}", e))?;
            
        Ok((ciphertext.to_vec(), encrypted))
    }

    pub fn decrypt_secret(&self, ct: &[u8], ciphertext: &[u8]) -> Result<Vec<u8>> {
        // Odzyskanie shared secret
        let shared_secret = decapsulate(ct, &self.kyber_sk);
        
        // Deszyfrowanie AES-GCM-SIV
        let key = aes_gcm_siv::Key::<Aes256GcmSiv>::from_slice(&shared_secret);
        let cipher = Aes256GcmSiv::new(key);
        let nonce = Nonce::from_slice(b"unique_nonce");
        
        let plaintext = cipher.decrypt(nonce, ciphertext)
            .map_err(|e| anyhow!("Decryption failed: {}", e))?;
            
        Ok(plaintext)
    }
}
