use std::{
    collections::{HashMap, HashSet},
    fs::{self},
    path::{Path, PathBuf},
    process::Command,
};
use anyhow::{Result, anyhow};
use chrono::Local;
use clap::{Parser, Subcommand};
use semver::{Version, VersionReq};
use toml_edit::{DocumentMut, Item, Table, value};
use serde_json::json;

const BACKUP_PREFIX: &str = "Cargo.toml.backup_";

#[derive(Parser)]
#[clap(
    name = "cargo-dependency-resolver",
    version = "1.1",
    about = "Advanced Rust dependency conflict resolver with security & minimalism enhancements",
    author = "THE OVERMIND PROTOCOL v4.1"
)]
struct Cli {
    #[clap(default_value = ".")]
    path: String,

    #[clap(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// Clean duplicates and resolve conflicts
    Clean {
        #[clap(short, long)]
        verbose: bool,
        
        #[clap(short, long)]
        update: bool,
        
        #[clap(short, long)]
        audit: bool,
        
        #[clap(short, long)]
        patch: bool,
        
        #[clap(long)]
        hotz: bool,
    },
    
    /// Restore from backup
    Restore {
        /// Backup timestamp (YYYYMMDD_HHMMSS) or "latest"
        #[clap(default_value = "latest")]
        timestamp: String,
    },
    
    /// Enhance security (patches + insecure crate replacement)
    Enhance {
        #[clap(short, long)]
        aggressive: bool,
        
        #[clap(short, long)]
        verbose: bool,
        
        #[clap(long)]
        sbom: bool,  // Generate SBOM report
    },
}

#[derive(Debug)]
struct DepInfo {
    version: String,
    source: Option<String>,
    required_by: HashSet<String>,
}

fn main() -> Result<()> {
    let cli = Cli::parse();
    
    match cli.command {
        Commands::Clean { verbose, update, audit, patch, hotz } => {
            clean_dependencies(&cli.path, verbose, update, audit, patch, hotz)?;
        },
        Commands::Restore { timestamp } => {
            restore_from_backup(&cli.path, &timestamp)?;
        },
        Commands::Enhance { aggressive, verbose, sbom } => {
            enhance_security(&cli.path, aggressive, verbose, sbom)?;
        },
    }

    Ok(())
}

fn clean_dependencies(
    project_path: &str,
    verbose: bool,
    run_update: bool,
    run_audit: bool,
    apply_patch: bool,
    apply_hotz: bool,
) -> Result<()> {
    let cargo_path = Path::new(project_path).join("Cargo.toml");
    let mut doc = read_and_backup_cargo(&cargo_path)?;
    
    let sections = vec!["dependencies", "dev-dependencies", "build-dependencies"];
    
    for section in sections {
        if let Some(deps) = doc.get_mut(section).and_then(Item::as_table_mut) {
            clean_section_duplicates(deps, verbose, section);
        }
        
        if let Some(workspace) = doc.get_mut("workspace") {
            if let Some(ws_deps) = workspace.get_mut(section).and_then(Item::as_table_mut) {
                clean_section_duplicates(ws_deps, verbose, &format!("workspace.{}", section));
            }
        }
    }
    
    if apply_hotz {
        apply_hotz_philosophy(&mut doc, verbose)?;
    }
    
    if apply_patch {
        apply_security_patches(&mut doc, verbose)?;
    }
    
    write_cargo_toml(&cargo_path, doc.to_string())?;
    
    if run_update {
        run_cargo_command(project_path, "update")?;
    }
    
    if run_audit {
        run_cargo_audit(project_path)?;
    }
    
    Ok(())
}

fn clean_section_duplicates(deps: &mut Table, verbose: bool, section: &str) {
    let mut unique = Table::new();
    for (name, item) in deps.iter() {
        if unique.contains_key(name) {
            if verbose {
                println!("Removed duplicate in {}: {}", section, name);
            }
        } else {
            unique.insert(name.clone(), item.clone());
        }
    }
    *deps = unique;
}

fn enhance_security(project_path: &str, aggressive: bool, verbose: bool, generate_sbom: bool) -> Result<()> {
    let cargo_path = Path::new(project_path).join("Cargo.toml");
    let mut doc = read_and_backup_cargo(&cargo_path)?;
    
    apply_security_patches(&mut doc, verbose)?;
    
    let insecure_crates = vec!["chrono", "time@0.1", "openssl-sys@0.9"];
    remove_insecure_crates(&mut doc, &insecure_crates, aggressive, verbose)?;
    
    write_cargo_toml(&cargo_path, doc.to_string())?;
    
    if generate_sbom {
        generate_sbom_report(project_path)?;
    }
    
    Ok(())
}

fn remove_insecure_crates(doc: &mut DocumentMut, insecure: &Vec<&str>, aggressive: bool, verbose: bool) -> Result<()> {
    let sections = vec!["dependencies", "dev-dependencies", "build-dependencies"];
    
    for section in sections {
        if let Some(deps) = doc.get_mut(section).and_then(Item::as_table_mut) {
            for crate_name in insecure {
                if deps.contains_key(*crate_name) {
                    if aggressive {
                        deps.remove(*crate_name);
                        if verbose {
                            println!("Removed insecure crate from {}: {}", section, crate_name);
                        }
                    } else {
                        if verbose {
                            println!("Warning: Insecure crate found in {}: {}", section, crate_name);
                        }
                    }
                }
            }
        }
    }
    
    Ok(())
}

fn generate_sbom_report(project_path: &str) -> Result<()> {
    let output = Command::new("cargo")
        .current_dir(project_path)
        .arg("metadata")
        .arg("--format-version=1")
        .output()?;
    
    let metadata = String::from_utf8_lossy(&output.stdout);
    let sbom = json!({
        "project": "THE OVERMIND PROTOCOL",
        "dependencies": metadata,
        "generated": Local::now().to_string(),
    });
    
    let report_path = Path::new(project_path).join("sbom.json");
    fs::write(report_path, serde_json::to_string_pretty(&sbom)?)?;
    println!("✓ SBOM report generated: sbom.json");
    
    Ok(())
}

fn read_and_backup_cargo(path: &Path) -> Result<DocumentMut> {
    let content = fs::read_to_string(path)?;
    
    let timestamp = Local::now().format("%Y%m%d_%H%M%S").to_string();
    let backup_path = path.with_file_name(format!("Cargo.toml.backup_{}", timestamp));
    fs::copy(path, &backup_path)?;
    
    println!("✓ Backup created: {:?}", backup_path);
    
    content.parse::<DocumentMut>()
        .map_err(|e| anyhow!("TOML parse error: {}", e))
}

fn write_cargo_toml(path: &Path, content: String) -> Result<()> {
    fs::write(path, content)?;
    Ok(())
}

fn apply_hotz_philosophy(doc: &mut DocumentMut, verbose: bool) -> Result<()> {
    if verbose {
        println!("Applying HOTZ philosophy (minimal dependencies)...");
    }
    
    let replacements = HashMap::from([
        ("reqwest", ("ureq", "0.6")),
        ("serde_json", ("json", "0.12")),
        ("rand", ("getrandom", "0.8")),
        ("chrono", ("time", "0.3")),
    ]);
    
    let sections = vec!["dependencies", "dev-dependencies", "build-dependencies"];
    
    for section in sections {
        if let Some(deps) = doc.get_mut(section).and_then(Item::as_table_mut) {
            for (bloated, (minimal, version)) in &replacements {
                if deps.contains_key(*bloated) && !deps.contains_key(*minimal) {
                    if verbose {
                        println!("HOTZ: Replacing {} with {} in {}", bloated, minimal, section);
                    }
                    deps.remove(*bloated);
                    deps.insert(minimal, value(version.to_string()));
                }
            }
        }
    }
    
    Ok(())
}

fn apply_security_patches(doc: &mut DocumentMut, verbose: bool) -> Result<()> {
    if verbose {
        println!("Adding security patches...");
    }

    let mut patches = Table::new();

    patches.insert("curve25519-dalek",
        value("{ git = \"https://github.com/dalek-cryptography/curve25519-dalek\", tag = \"v4.1.3\" }"));
    patches.insert("ed25519-dalek",
        value("{ git = \"https://github.com/dalek-cryptography/ed25519-dalek\", tag = \"v2.2.0\" }"));

    patches.insert("ring",
        value("{ git = \"https://github.com/briansmith/ring\", branch = \"main\" }"));
    patches.insert("openssl",
        value("{ version = \"0.10.66\", features = [\"vendored\"] }"));

    let mut patch_table = Table::new();
    patch_table.insert("crates-io", Item::Table(patches));

    doc.as_table_mut().insert("patch", Item::Table(patch_table));

    println!("✓ Added security patches to [patch.crates-io].");

    Ok(())
}

fn restore_from_backup(project_path: &str, timestamp: &str) -> Result<()> {
    let cargo_path = Path::new(project_path).join("Cargo.toml");

    let backup_path = if timestamp == "latest" {
        find_latest_backup(project_path)?
    } else {
        Path::new(project_path).join(format!("Cargo.toml.backup_{}", timestamp))
    };

    if !backup_path.exists() {
        return Err(anyhow!("Backup not found: {:?}", backup_path));
    }

    fs::copy(&backup_path, &cargo_path)?;
    println!("✓ Restored from backup: {:?}", backup_path);

    Ok(())
}

fn find_latest_backup(project_path: &str) -> Result<PathBuf> {
    let dir = fs::read_dir(project_path)?;
    let mut backups = Vec::new();

    for entry in dir {
        let entry = entry?;
        let name = entry.file_name();
        let name_str = name.to_string_lossy();

        if name_str.starts_with(BACKUP_PREFIX) {
            backups.push(entry.path());
        }
    }

    backups.sort();
    backups.last()
        .cloned()
        .ok_or_else(|| anyhow!("No backups found"))
}

fn run_cargo_audit(project_path: &str) -> Result<()> {
    let status = Command::new("cargo")
        .args(&["audit", "--deny", "warnings"])
        .current_dir(project_path)
        .status()?;

    if !status.success() {
        return Err(anyhow!(
            "cargo audit failed with status: {}",
            status.code().unwrap_or(1)
        ));
    }

    println!("🔒 Security audit passed!");
    Ok(())
}

fn run_cargo_command(project_path: &str, command: &str) -> Result<()> {
    let status = Command::new("cargo")
        .arg(command)
        .current_dir(project_path)
        .status()?;

    if !status.success() {
        return Err(anyhow!(
            "cargo {} failed with status: {}",
            command,
            status.code().unwrap_or(1)
        ));
    }

    println!("✓ Ran cargo {}.", command);
    Ok(())
}
