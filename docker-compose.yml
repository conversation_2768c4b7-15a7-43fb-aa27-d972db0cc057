# THE OVERMIND PROTOCOL - Production Setup with Monitoring
version: '3.8'

services:
  # AI Brain - Python decision engine
  ai-brain:
    build: ./brain
    container_name: overmind-ai-brain
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - HELIUS_API_KEY=${HELIUS_API_KEY}
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - JINA_API_KEY=${JINA_API_KEY}
      - DRAGONFLY_URL=redis://dragonfly:6379
    depends_on:
      - dragonfly
      - chroma
    networks:
      - trading-network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

  # Trading Executor - Rust HFT engine
  trading-executor:
    build: .
    container_name: overmind-trading-executor
    environment:
      - SNIPER_TRADING_MODE=${SNIPER_TRADING_MODE:-paper}
      - OVERMIND_AI_MODE=${OVERMIND_AI_MODE:-enabled}
      - DRAGONFLY_URL=redis://dragonfly:6379
      - SOLANA_RPC_URL=${SOLANA_RPC_URL}
      - HELIUS_API_KEY=${HELIUS_API_KEY}
      - JITO_API_KEY=${JITO_API_KEY}
      # MICRO-LIGHTNING specific configuration
      - MICRO_LIGHTNING_ENABLED=${MICRO_LIGHTNING_ENABLED:-true}
      - MICRO_CAPITAL_ALLOCATION=${MICRO_CAPITAL_ALLOCATION:-20.0}
      - MICRO_MAX_HOLD_TIME=${MICRO_MAX_HOLD_TIME:-55}
      - MICRO_EMERGENCY_SLIPPAGE=${MICRO_EMERGENCY_SLIPPAGE:-45.0}
      - MICRO_DAILY_OPERATION_LIMIT=${MICRO_DAILY_OPERATION_LIMIT:-5}
    ports:
      - "8080:8080"
    depends_on:
      - dragonfly
      - prometheus
      - micro-lightning-monitor
    networks:
      - trading-network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

  # MICRO-LIGHTNING Monitor - Specialized monitoring for micro operations
  micro-lightning-monitor:
    build:
      context: .
      dockerfile: Dockerfile.micro-lightning
    container_name: overmind-micro-lightning-monitor
    environment:
      - MICRO_LIGHTNING_MODE=${MICRO_LIGHTNING_MODE:-active}
      - DRAGONFLY_URL=redis://dragonfly:6379
      - PROMETHEUS_URL=http://prometheus:9090
      - ALERT_WEBHOOK_URL=${MICRO_LIGHTNING_ALERT_WEBHOOK}
      # 5 Commandments Configuration
      - COMMANDMENT_LIFE_LIMIT=55
      - COMMANDMENT_WALLET_ROTATION=3
      - COMMANDMENT_MILITIA_COOLDOWN=30
      - COMMANDMENT_PSYCHOLOGY_TAX=0.10
      - COMMANDMENT_BATTLEFIELD_MIN=2000
      - COMMANDMENT_BATTLEFIELD_MAX=10000
    ports:
      - "8081:8081"
    depends_on:
      - dragonfly
      - prometheus
    networks:
      - trading-network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"

  # DragonflyDB - High-performance Redis
  dragonfly:
    image: docker.dragonflydb.io/dragonflydb/dragonfly:latest
    container_name: overmind-dragonfly
    ports:
      - "6379:6379"
    networks:
      - trading-network
    restart: unless-stopped
    volumes:
      - dragonfly-data:/data

  # Chroma Vector Database - AI Memory
  chroma:
    image: chromadb/chroma:latest
    container_name: overmind-chroma
    ports:
      - "8000:8000"
    networks:
      - trading-network
    restart: unless-stopped
    volumes:
      - chroma-data:/chroma/chroma

  # Prometheus - Metrics collection
  prometheus:
    image: prom/prometheus:latest
    container_name: overmind-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - trading-network
    restart: unless-stopped

  # Grafana - Visualization dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: overmind-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-overmind123}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./config/grafana:/etc/grafana/provisioning
    networks:
      - trading-network
    restart: unless-stopped
    depends_on:
      - prometheus

  # AlertManager - Alert handling
  alertmanager:
    image: prom/alertmanager:latest
    container_name: overmind-alertmanager
    ports:
      - "9093:9093"
    volumes:
      - ./config/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager-data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
    networks:
      - trading-network
    restart: unless-stopped

  # Node Exporter - System metrics
  node-exporter:
    image: prom/node-exporter:latest
    container_name: overmind-node-exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - trading-network
    restart: unless-stopped

volumes:
  dragonfly-data:
  chroma-data:
  prometheus-data:
  grafana-data:
  alertmanager-data:

networks:
  trading-network:
    driver: bridge
