[workspace]
members = [".", "chimera_client", "overmind_cortex"]
resolver = "2"

[package]
name = "overmind-protocol"
version = "4.3.0"  # SOLUTION: Updated to v4.3.0 MONOLITH VERIFIED
edition = "2021"
authors = ["THE OVERMIND PROTOCOL Team"]
description = "THE OVERMIND PROTOCOL v4.1 'MONOLITH' - Autonomous AI Trading System"
license = "MIT"
repository = "https://github.com/SynergiaOS/TradingBot-Clean"

[lib]
name = "overmind_protocol"
path = "src/lib.rs"

[[bin]]
name = "overmind-protocol"
path = "src/main.rs"

[[bin]]
name = "micro-lightning-monitor"
path = "src/bin/micro-lightning-monitor.rs"

[[bin]]
name = "phoenix-memcoin-bot"
path = "src/bin/phoenix-memcoin-bot.rs"

[[bin]]
name = "neural-execution-demo"
path = "src/bin/neural-execution-demo.rs"

[[bin]]
name = "neural-execution-optimized-demo"
path = "src/bin/neural-execution-optimized-demo.rs"

[[bin]]
name = "test-qdrant-connection"
path = "test_qdrant_connection.rs"

[[bin]]
name = "test-qdrant-rust-client"
path = "test_qdrant_rust_client.rs"

[[bin]]
name = "test-qdrant-simple"
path = "test_qdrant_simple.rs"

[dependencies]
tokio = { version = "1.35", features = ["full"] }
solana-sdk = { version = "2.3", features = ["full"] }  # CRITICAL: Full features required for modules
solana-client = "2.3"
axum = "0.7"
tower = "0.4"
tower-http = { version = "0.5", features = ["cors"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0.107"  # FIXED: Specific stable version
tinyjson = "2.5"
anyhow = "1.0"
thiserror = "1.0"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
reqwest = { version = "0.12", features = ["json"] }  # FIXED: Updated to v0.12
http = "0.2"  # FIXED: Added missing http dependency
hyper = { version = "0.14", features = ["full"] }  # FIXED: Added missing hyper dependency
minreq = "2.11"
tokio-tungstenite = "0.21"
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
futures = "0.3"
futures-util = "0.3"
url = "2.5"
dotenvy = "0.15"
rand = "0.8"
nanorand = "0.7"
bs58 = "0.5"
hex = "0.4"
candle-core = { git = "https://github.com/huggingface/candle.git" }
candle-nn = { git = "https://github.com/huggingface/candle.git" }
candle-transformers = { git = "https://github.com/huggingface/candle.git" }
hf-hub = "0.3"
tokenizers = "0.19"
qdrant-client = "1.7"  # Much older version to avoid zeroize conflicts
async-trait = "0.1"
dashmap = "5.5"
parking_lot = "0.12"
crossbeam-channel = "0.5"
jito-sdk-rust = "0.3.2"  # VERIFIED: Latest stable for MEV optimization
base64 = "0.22"
bincode = "1.3"
redis = { version = "0.24", features = ["tokio-comp", "connection-manager", "streams", "aio"] }
toml = "0.8"
config = "0.14"
aes-gcm = "0.10"
generic-array = "0.14"
exponential-backoff = "2.1"  # VERIFIED: Latest stable v2.1 for retry logic
moka = { version = "0.12", features = ["future"] }
sha2 = "0.10.9"
sha3 = "0.10"
clap = { version = "4.5.40", features = ["derive"] }
kyber-rs = { version = "0.1.0-alpha.9", features = [] }  # UPDATED: Using latest available pre-release
pqcrypto-traits = "0.3.5"  # FIXED: Latest available stable version
pqcrypto-mlkem = "0.1.0"  # FIXED: Only available version (latest stable)
libloading = "0.8.8"
tempfile = "3.0"
aes-gcm-siv = "0.11"  # VERIFIED: Hybrid encryption for QuantumVault
simsimd = "6.5.0"  # VERIFIED: SIMD operations for hardware acceleration
is-terminal = "0.4"  # Replaces atty
nu-ansi-term = "0.50"  # Replaces ansi_term
curve25519-dalek = "4.1.3"  # CRITICAL: Fix timing attack vulnerability
ed25519-dalek = "2.1.1"     # CRITICAL: Fix double public key signing vulnerability
semver = "1.0"           # For version parsing
toml_edit = "0.22"       # For TOML manipulation

[dev-dependencies]
tokio-test = "0.4"
proptest = "1.0"
mockall = "0.12"
criterion = { version = "0.5", features = ["html_reports"] }  # VERIFIED: Benchmarking framework
tempfile = "3.0"
wiremock = "0.5"
serde_json = "1.0"  # For SBOM testing
axum = "0.7"
futures = "0.3"
anyhow = "1.0"

[profile.release]
lto = true
codegen-units = 1
panic = "abort"
opt-level = 3

[profile.contabo]
inherits = "release"
opt-level = 3
lto = "fat"

# THE OVERMIND PROTOCOL v4.4 "GEOHOT CORE" - Hotz Philosophy Profiles
[profile.geo_hot]
inherits = "release"
opt-level = 3
lto = "fat"
codegen-units = 1
panic = "abort"

[[bench]]
name = "overmind_benchmarks"
harness = false



# FIXED: Removed duplicate dependencies - all consolidated in main [dependencies] section above

