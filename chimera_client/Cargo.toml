[package]
name = "chimera_client"
version = "0.1.0"
edition = "2021"
authors = ["THE OVERMIND PROTOCOL Team"]
description = "CHIMERA Client - AI Communication Bridge for THE OVERMIND PROTOCOL"
license = "MIT"

[dependencies]
# Core async runtime
tokio = { version = "1.35", features = ["full"] }

# HTTP client - HOTZ PHILOSOPHY: Zero dependencies
minreq = "2.11"

# Serialization - HOTZ PHILOSOPHY: Minimal JSON (keeping serde_json for compatibility)
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tinyjson = "2.5"

# Error handling
thiserror = "1.0"

# Logging
tracing = "0.1"

# Random number generation - HOTZ PHILOSOPHY: Zero dependencies
nanorand = "0.7"

# UUID generation
uuid = { version = "1.0", features = ["v4"] }

# Time utilities
chrono = { version = "0.4", features = ["serde"] }

# Encryption support (optional)
aes-gcm = { version = "0.10", optional = true }

[features]
default = []
geo_encryption = ["aes-gcm"]

[dev-dependencies]
# Testing framework
tokio-test = "0.4"
tracing-subscriber = "0.3"

[[example]]
name = "basic_usage"
path = "examples/basic_usage.rs"

[[example]]
name = "overmind_integration"
path = "examples/overmind_integration.rs"
