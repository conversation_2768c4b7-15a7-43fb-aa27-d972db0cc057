# OPERACJA "FORGE" - TensorZero Strategy Compilation Pipeline
# 
# Atomowa kuźnia inteligencji - kompilacja DSL strategii do natywnych modułów bojowych
# Hardware-aware optimization + formal verification

name: 🔥 FORGE - Strategy Compiler

on:
  push:
    paths:
      - 'strategies/**/*.dsl'
      - 'src/forge/**'
      - '.github/workflows/forge.yml'
  pull_request:
    paths:
      - 'strategies/**/*.dsl'
      - 'src/forge/**'
  workflow_dispatch:
    inputs:
      strategy_file:
        description: 'Strategy DSL file to compile'
        required: true
        default: 'strategies/momentum_v1.dsl'
      target_arch:
        description: 'Target architecture'
        required: true
        default: 'x86_64-unknown-linux-gnu'
        type: choice
        options:
          - x86_64-unknown-linux-gnu
          - x86_64-pc-windows-msvc
          - aarch64-unknown-linux-gnu
      optimization_level:
        description: 'Optimization level'
        required: true
        default: 'release'
        type: choice
        options:
          - debug
          - release
          - release-lto
          - release-avx512

env:
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: 1
  TENSORZERO_VERSION: "1.4.0"
  ARTIFACT_BUCKET: "overmind-forge-artifacts"
  AWS_REGION: "us-east-1"

jobs:
  # FAZA 1A: Przygotowanie środowiska kompilacji
  setup-forge:
    name: 🔧 Setup FORGE Environment
    runs-on: ubuntu-latest
    outputs:
      strategy-files: ${{ steps.detect-strategies.outputs.files }}
      tensorzero-cache-key: ${{ steps.cache-key.outputs.key }}
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: 🔍 Detect Strategy Files
      id: detect-strategies
      run: |
        if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
          echo "files=[\"${{ github.event.inputs.strategy_file }}\"]" >> $GITHUB_OUTPUT
        else
          # Detect changed .dsl files
          CHANGED_FILES=$(git diff --name-only ${{ github.event.before }}..${{ github.sha }} | grep '\.dsl$' || echo "")
          if [ -z "$CHANGED_FILES" ]; then
            # If no DSL files changed, compile all strategies
            FILES=$(find strategies -name "*.dsl" | jq -R -s -c 'split("\n")[:-1]')
          else
            FILES=$(echo "$CHANGED_FILES" | jq -R -s -c 'split("\n")[:-1]')
          fi
          echo "files=$FILES" >> $GITHUB_OUTPUT
        fi
    
    - name: 🔑 Generate Cache Key
      id: cache-key
      run: |
        HASH=$(echo "${{ env.TENSORZERO_VERSION }}-${{ runner.os }}" | sha256sum | cut -d' ' -f1)
        echo "key=tensorzero-$HASH" >> $GITHUB_OUTPUT

  # FAZA 1B: Instalacja TensorZero Compiler
  install-tensorzero:
    name: 🧠 Install TensorZero Compiler
    runs-on: ubuntu-latest
    needs: setup-forge
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
    
    - name: 💾 Cache TensorZero
      id: cache-tensorzero
      uses: actions/cache@v3
      with:
        path: |
          ~/.tensorzero
          ~/.cargo/bin/tzc
        key: ${{ needs.setup-forge.outputs.tensorzero-cache-key }}
    
    - name: 🔧 Install TensorZero Compiler
      if: steps.cache-tensorzero.outputs.cache-hit != 'true'
      run: |
        # Download TensorZero compiler
        curl -L "https://github.com/tensorzero/tensorzero/releases/download/v${{ env.TENSORZERO_VERSION }}/tensorzero-${{ env.TENSORZERO_VERSION }}-x86_64-unknown-linux-gnu.tar.gz" \
          -o tensorzero.tar.gz
        
        # Extract and install
        tar -xzf tensorzero.tar.gz
        mkdir -p ~/.tensorzero ~/.cargo/bin
        mv tensorzero-${{ env.TENSORZERO_VERSION }}/tzc ~/.cargo/bin/
        mv tensorzero-${{ env.TENSORZERO_VERSION }}/* ~/.tensorzero/
        chmod +x ~/.cargo/bin/tzc
        
        # Verify installation
        ~/.cargo/bin/tzc --version
    
    - name: 🧪 Test TensorZero Installation
      run: |
        export PATH="$HOME/.cargo/bin:$PATH"
        tzc --version
        tzc --help

  # FAZA 1C: Kompilacja strategii DSL
  compile-strategies:
    name: ⚙️ Compile Strategy DSL
    runs-on: ubuntu-latest
    needs: [setup-forge, install-tensorzero]
    if: needs.setup-forge.outputs.strategy-files != '[]'
    strategy:
      matrix:
        strategy-file: ${{ fromJson(needs.setup-forge.outputs.strategy-files) }}
        target: 
          - x86_64-unknown-linux-gnu
          - aarch64-unknown-linux-gnu
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
    
    - name: 🦀 Setup Rust Toolchain
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        target: ${{ matrix.target }}
        override: true
        components: rustfmt, clippy
    
    - name: 💾 Restore TensorZero Cache
      uses: actions/cache@v3
      with:
        path: |
          ~/.tensorzero
          ~/.cargo/bin/tzc
        key: ${{ needs.setup-forge.outputs.tensorzero-cache-key }}
    
    - name: 🔧 Setup Compilation Environment
      run: |
        export PATH="$HOME/.cargo/bin:$PATH"
        
        # Install cross-compilation tools
        if [ "${{ matrix.target }}" = "aarch64-unknown-linux-gnu" ]; then
          sudo apt-get update
          sudo apt-get install -y gcc-aarch64-linux-gnu
          export CC_aarch64_unknown_linux_gnu=aarch64-linux-gnu-gcc
        fi
        
        # Install additional dependencies for hardware optimization
        sudo apt-get install -y \
          build-essential \
          pkg-config \
          libssl-dev \
          llvm-dev \
          libclang-dev
    
    - name: 📝 Validate Strategy DSL
      run: |
        export PATH="$HOME/.cargo/bin:$PATH"
        
        echo "🔍 Validating strategy DSL: ${{ matrix.strategy-file }}"
        
        # Basic syntax validation
        if [ ! -f "${{ matrix.strategy-file }}" ]; then
          echo "❌ Strategy file not found: ${{ matrix.strategy-file }}"
          exit 1
        fi
        
        # TensorZero DSL validation
        tzc validate "${{ matrix.strategy-file }}"
        
        echo "✅ DSL validation passed"
    
    - name: ⚙️ Compile Strategy to Native Module
      run: |
        export PATH="$HOME/.cargo/bin:$PATH"
        
        STRATEGY_FILE="${{ matrix.strategy-file }}"
        STRATEGY_NAME=$(basename "$STRATEGY_FILE" .dsl)
        TARGET="${{ matrix.target }}"
        
        echo "🔨 Compiling strategy: $STRATEGY_NAME for target: $TARGET"
        
        # Determine optimization flags based on target
        if [[ "$TARGET" == *"x86_64"* ]]; then
          OPTIMIZATION_FLAGS="-C target-cpu=native -C target-feature=+avx2,+fma,+sse4.2"
          if [ "${{ github.event.inputs.optimization_level }}" = "release-avx512" ]; then
            OPTIMIZATION_FLAGS="$OPTIMIZATION_FLAGS,+avx512f,+avx512dq"
          fi
        else
          OPTIMIZATION_FLAGS="-C target-cpu=native"
        fi
        
        # Compile DSL to Rust source
        mkdir -p target/forge/$STRATEGY_NAME
        tzc compile \
          --input "$STRATEGY_FILE" \
          --output "target/forge/$STRATEGY_NAME/lib.rs" \
          --target "$TARGET" \
          --optimization-level "${{ github.event.inputs.optimization_level || 'release' }}" \
          --enable-simd \
          --enable-lto
        
        # Generate Cargo.toml for the strategy
        cat > "target/forge/$STRATEGY_NAME/Cargo.toml" << EOF
        [package]
        name = "$STRATEGY_NAME"
        version = "1.0.0"
        edition = "2021"
        
        [lib]
        crate-type = ["cdylib"]
        
        [dependencies]
        serde = { version = "1.0", features = ["derive"] }
        serde_json = "1.0"
        
        [profile.release]
        opt-level = 3
        lto = true
        codegen-units = 1
        panic = "abort"
        strip = true
        
        [target.$TARGET.rustflags]
        rustflags = ["$OPTIMIZATION_FLAGS"]
        EOF
        
        # Compile to shared library
        cd "target/forge/$STRATEGY_NAME"
        RUSTFLAGS="$OPTIMIZATION_FLAGS" cargo build \
          --release \
          --target "$TARGET"
        
        # Copy compiled artifact
        ARTIFACT_NAME="${STRATEGY_NAME}_${TARGET}_$(date +%Y%m%d_%H%M%S).so"
        cp "target/$TARGET/release/lib$STRATEGY_NAME.so" "../../../$ARTIFACT_NAME"
        
        echo "✅ Compilation successful: $ARTIFACT_NAME"
        echo "ARTIFACT_NAME=$ARTIFACT_NAME" >> $GITHUB_ENV
    
    - name: 🔍 Verify Compiled Artifact
      run: |
        ARTIFACT_NAME="${{ env.ARTIFACT_NAME }}"
        
        echo "🔍 Verifying compiled artifact: $ARTIFACT_NAME"
        
        # Check file exists and is executable
        if [ ! -f "$ARTIFACT_NAME" ]; then
          echo "❌ Artifact not found: $ARTIFACT_NAME"
          exit 1
        fi
        
        # Check file type
        file "$ARTIFACT_NAME"
        
        # Check symbols
        nm -D "$ARTIFACT_NAME" | grep -E "(strategy_|analyze|execute)" || true
        
        # Calculate checksum
        CHECKSUM=$(sha256sum "$ARTIFACT_NAME" | cut -d' ' -f1)
        echo "📊 Artifact checksum: $CHECKSUM"
        echo "ARTIFACT_CHECKSUM=$CHECKSUM" >> $GITHUB_ENV
        
        # Get file size
        SIZE=$(stat -c%s "$ARTIFACT_NAME")
        echo "📏 Artifact size: $SIZE bytes"
        echo "ARTIFACT_SIZE=$SIZE" >> $GITHUB_ENV
    
    - name: 📤 Upload Artifact
      uses: actions/upload-artifact@v3
      with:
        name: strategy-${{ matrix.strategy-file }}-${{ matrix.target }}
        path: ${{ env.ARTIFACT_NAME }}
        retention-days: 30
    
    - name: 📊 Generate Compilation Report
      run: |
        cat > compilation-report.json << EOF
        {
          "strategy_file": "${{ matrix.strategy-file }}",
          "target": "${{ matrix.target }}",
          "artifact_name": "${{ env.ARTIFACT_NAME }}",
          "checksum": "${{ env.ARTIFACT_CHECKSUM }}",
          "size_bytes": ${{ env.ARTIFACT_SIZE }},
          "compilation_time": "$(date -Iseconds)",
          "optimization_level": "${{ github.event.inputs.optimization_level || 'release' }}",
          "tensorzero_version": "${{ env.TENSORZERO_VERSION }}",
          "commit_sha": "${{ github.sha }}"
        }
        EOF
        
        echo "📊 Compilation Report:"
        cat compilation-report.json
    
    - name: 📤 Upload Compilation Report
      uses: actions/upload-artifact@v3
      with:
        name: compilation-report-${{ matrix.strategy-file }}-${{ matrix.target }}
        path: compilation-report.json

  # FAZA 1D: Publikacja do Artifact Repository
  publish-artifacts:
    name: 📦 Publish to Artifact Repository
    runs-on: ubuntu-latest
    needs: [setup-forge, compile-strategies]
    if: github.ref == 'refs/heads/main' && needs.setup-forge.outputs.strategy-files != '[]'
    
    steps:
    - name: 📥 Download All Artifacts
      uses: actions/download-artifact@v3
    
    - name: 🔧 Configure AWS CLI
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}
    
    - name: 📦 Publish to S3 Artifact Repository
      run: |
        echo "📦 Publishing artifacts to S3..."
        
        # Create S3 bucket if it doesn't exist
        aws s3 mb s3://${{ env.ARTIFACT_BUCKET }} --region ${{ env.AWS_REGION }} || true
        
        # Upload all compiled artifacts
        for artifact_dir in strategy-*; do
          if [ -d "$artifact_dir" ]; then
            echo "📤 Uploading artifacts from: $artifact_dir"
            
            # Upload .so files
            find "$artifact_dir" -name "*.so" -exec aws s3 cp {} s3://${{ env.ARTIFACT_BUCKET }}/strategies/ \;
            
            # Upload compilation reports
            find "$artifact_dir" -name "compilation-report.json" -exec aws s3 cp {} s3://${{ env.ARTIFACT_BUCKET }}/reports/ \;
          fi
        done
        
        echo "✅ All artifacts published successfully"
    
    - name: 🏷️ Create Release Tag
      if: github.event_name == 'push'
      run: |
        TAG_NAME="forge-$(date +%Y%m%d-%H%M%S)"
        echo "🏷️ Creating release tag: $TAG_NAME"
        
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git tag -a "$TAG_NAME" -m "FORGE compilation artifacts - $(date)"
        git push origin "$TAG_NAME"

  # FAZA 1E: Testy integracyjne
  integration-tests:
    name: 🧪 Integration Tests
    runs-on: ubuntu-latest
    needs: [setup-forge, compile-strategies]
    if: needs.setup-forge.outputs.strategy-files != '[]'
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
    
    - name: 🦀 Setup Rust
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        override: true
    
    - name: 📥 Download Compiled Artifacts
      uses: actions/download-artifact@v3
    
    - name: 🧪 Test Dynamic Loading
      run: |
        echo "🧪 Testing dynamic loading of compiled strategies..."
        
        # Build test harness
        cargo build --bin forge-test-harness
        
        # Test each compiled artifact
        for artifact_dir in strategy-*; do
          if [ -d "$artifact_dir" ]; then
            SO_FILE=$(find "$artifact_dir" -name "*.so" | head -1)
            if [ -n "$SO_FILE" ]; then
              echo "🔍 Testing artifact: $SO_FILE"
              ./target/debug/forge-test-harness "$SO_FILE"
            fi
          fi
        done
        
        echo "✅ All integration tests passed"

# Notification na końcu
  notify-completion:
    name: 📢 Notify Completion
    runs-on: ubuntu-latest
    needs: [setup-forge, compile-strategies, publish-artifacts, integration-tests]
    if: always()
    
    steps:
    - name: 📢 Send Notification
      run: |
        if [ "${{ needs.compile-strategies.result }}" = "success" ]; then
          echo "🎉 OPERACJA 'FORGE' - FAZA 1 ZAKOŃCZONA SUKCESEM!"
          echo "✅ Wszystkie strategie skompilowane i opublikowane"
        else
          echo "❌ OPERACJA 'FORGE' - BŁĘDY KOMPILACJI"
          echo "🔍 Sprawdź logi dla szczegółów"
        fi
