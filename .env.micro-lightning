# MICRO-LIGHTNING TRADING SYSTEM CONFIGURATION
# OPERACJA MIKRO-BŁYSKAWICA Environment Variables

# ============================================================================
# CORE SYSTEM CONFIGURATION
# ============================================================================

# Trading mode (paper/live)
SNIPER_TRADING_MODE=paper
OVERMIND_AI_MODE=enabled

# Micro-Lightning specific settings
MICRO_LIGHTNING_ENABLED=true
MICRO_LIGHTNING_MODE=active

# ============================================================================
# CAPITAL ALLOCATION ($20 SYSTEM)
# ============================================================================

# Total capital allocation for micro operations
MICRO_CAPITAL_ALLOCATION=20.0

# Wallet allocations (percentages)
MICRO_LIGHTNING_WALLET_PCT=0.20      # $4.0 - Primary trading
MICRO_EMERGENCY_GAS_PCT=0.175        # $3.5 - Emergency gas
MICRO_REENTRY_BUFFER_PCT=0.225       # $4.5 - Re-entry buffer
MICRO_PSYCHOLOGY_FUND_PCT=0.20       # $4.0 - Psychology fund
MICRO_TACTICAL_EXIT_PCT=0.20         # $4.0 - Tactical exit

# ============================================================================
# THE 5 COMMANDMENTS CONFIGURATION
# ============================================================================

# Commandment 1: Life Limit (Nakaz Życia)
COMMANDMENT_LIFE_LIMIT=55                    # Maximum hold time in minutes
COMMANDMENT_MIN_INTERVAL=55                  # Minimum interval between operations

# Commandment 2: Wallet Reincarnation (Nakaz Reinkarnacji)
COMMANDMENT_WALLET_ROTATION=3                # Max operations per wallet
COMMANDMENT_ROTATION_COOLDOWN=30             # Cooldown between rotations (minutes)

# Commandment 3: Militia Strategy (Nakaz Milicji)
COMMANDMENT_MILITIA_COOLDOWN=30              # Cooldown after losses (minutes)
COMMANDMENT_MAX_CONSECUTIVE_LOSSES=3         # Max consecutive losses before cooldown

# Commandment 4: Emotional Accounting (Nakaz Rachunku Emocji)
COMMANDMENT_PSYCHOLOGY_TAX=0.10              # Psychology tax rate (10%)
COMMANDMENT_MIN_PSYCHOLOGY_FUND=2.0          # Minimum psychology fund balance

# Commandment 5: Battlefield Selection (Nakaz Wyboru Pola Bitwy)
COMMANDMENT_BATTLEFIELD_MIN=2000             # Minimum liquidity ($)
COMMANDMENT_BATTLEFIELD_MAX=10000            # Maximum liquidity ($)
COMMANDMENT_MIN_HOLDERS=50                   # Minimum holder count
COMMANDMENT_MAX_HOLDERS=500                  # Maximum holder count

# ============================================================================
# ENTRY CONDITIONS CONFIGURATION
# ============================================================================

# Token age limits
MICRO_MAX_TOKEN_AGE=15                       # Maximum token age in minutes
MICRO_MIN_TOKEN_AGE=1                        # Minimum token age in minutes

# Social validation
MICRO_MIN_SOCIAL_MENTIONS=30                 # Minimum social mentions required
MICRO_MIN_SENTIMENT_SCORE=0.3                # Minimum sentiment score

# Risk parameters
MICRO_MAX_RISK_SCORE=0.6                     # Maximum acceptable risk score
MICRO_MIN_QUALITY_SCORE=0.4                  # Minimum quality score

# Creator validation
MICRO_REQUIRE_SINGLE_CREATOR_TXN=true        # Require exactly 1 creator transaction
MICRO_HONEYPOT_CHECK_ENABLED=true            # Enable honeypot detection

# ============================================================================
# TIME PROTOCOL CONFIGURATION
# ============================================================================

# Trading windows
MICRO_GOLDEN_WINDOW_END=15                   # Golden window end (minutes)
MICRO_DECAY_WINDOW_END=45                    # Decay window end (minutes)
MICRO_HARD_EXPIRY=55                         # Hard expiry time (minutes)

# Decay parameters
MICRO_DECAY_INTERVAL=5                       # Decay check interval (minutes)
MICRO_DECAY_PERCENTAGE=0.33                  # Percentage to exit per interval

# Emergency buffer
MICRO_EMERGENCY_EXIT_BUFFER=5                # Buffer before hard expiry (minutes)

# ============================================================================
# EMERGENCY PROTOCOLS CONFIGURATION
# ============================================================================

# Emergency triggers
MICRO_CREATOR_SELL_THRESHOLD=0.05            # Creator sell threshold (5% of supply)
MICRO_LIQUIDITY_DROP_THRESHOLD=0.30          # Liquidity drop threshold (30%)
MICRO_PRICE_DROP_THRESHOLD=0.40              # Price drop threshold (40%)
MICRO_HONEYPOT_CONFIDENCE_THRESHOLD=0.8      # Honeypot confidence threshold

# Emergency response
MICRO_EMERGENCY_SLIPPAGE=45.0                # Emergency exit slippage (%)
MICRO_MAX_EMERGENCY_EXECUTION_TIME=30        # Max emergency execution time (seconds)

# Circuit breaker
MICRO_CIRCUIT_BREAKER_DURATION=30            # Circuit breaker duration (minutes)
MICRO_MAX_TRANSACTION_FAILURES=3             # Max failed transactions before emergency

# ============================================================================
# EXIT SYSTEM CONFIGURATION
# ============================================================================

# Take-profit levels
MICRO_TP_LEVEL_1_THRESHOLD=0.15              # 15% profit threshold
MICRO_TP_LEVEL_1_EXIT=0.25                   # Exit 25% of position

MICRO_TP_LEVEL_2_THRESHOLD=0.35              # 35% profit threshold
MICRO_TP_LEVEL_2_EXIT=0.40                   # Exit 40% of position

MICRO_TP_LEVEL_3_THRESHOLD=0.60              # 60% profit threshold
MICRO_TP_LEVEL_3_EXIT=0.50                   # Exit 50% of position

MICRO_TP_LEVEL_4_THRESHOLD=1.00              # 100% profit threshold
MICRO_TP_LEVEL_4_EXIT=0.75                   # Exit 75% of position

# Volatility circuit breaker
MICRO_VOLATILITY_THRESHOLD=0.25              # Volatility threshold (25%)
MICRO_RED_CANDLE_THRESHOLD=3                 # Red candle threshold
MICRO_VOLUME_SPIKE_THRESHOLD=3.0             # Volume spike threshold

# Sentiment collapse detector
MICRO_NEGATIVE_SENTIMENT_THRESHOLD=-0.7      # Negative sentiment threshold
MICRO_NEGATIVE_MENTION_THRESHOLD=15          # Negative mention count threshold

# ============================================================================
# EXECUTION CONFIGURATION
# ============================================================================

# Position sizing
MICRO_DEFAULT_POSITION_SIZE_RATIO=0.8        # 80% of lightning wallet
MICRO_DEFAULT_REENTRY_BOOST_RATIO=0.6        # 60% of reentry wallet
MICRO_DEFAULT_DLMM_RATIO=0.375               # 37.5% of tactical wallet

# Execution parameters
MICRO_DEFAULT_SLIPPAGE=3.5                   # Default slippage (%)
MICRO_MAX_SLIPPAGE=10.0                      # Maximum slippage (%)
MICRO_PRIORITY_FEE_MULTIPLIER=1.5            # Priority fee multiplier

# DEX preferences
MICRO_PREFERRED_DEX=Raydium                  # Preferred DEX
MICRO_BACKUP_DEX=Meteora                     # Backup DEX

# Concurrent operations
MICRO_MAX_CONCURRENT_POSITIONS=1             # Maximum concurrent positions

# ============================================================================
# MONITORING AND ALERTING
# ============================================================================

# Performance targets
MICRO_TARGET_WIN_RATE=0.58                   # Target win rate (58%)
MICRO_TARGET_AVG_PROFIT=2.85                 # Target average profit ($)
MICRO_TARGET_MAX_DRAWDOWN=0.068              # Target max drawdown (6.8%)
MICRO_TARGET_SURVIVAL_RATE=0.92              # Target survival rate (92%)

# Alert thresholds
MICRO_LOW_WIN_RATE_THRESHOLD=0.50            # Low win rate alert threshold
MICRO_HIGH_LATENCY_THRESHOLD=120             # High latency alert threshold (ms)
MICRO_MAX_DAILY_LOSS=4.0                     # Maximum daily loss ($)

# Monitoring intervals
MICRO_METRICS_COLLECTION_INTERVAL=5          # Metrics collection interval (seconds)
MICRO_HEALTH_CHECK_INTERVAL=30               # Health check interval (seconds)
MICRO_ALERT_CHECK_INTERVAL=10                # Alert check interval (seconds)

# Webhook configuration
MICRO_LIGHTNING_ALERT_WEBHOOK=               # Alert webhook URL (optional)
MICRO_DISCORD_WEBHOOK=                       # Discord webhook URL (optional)
MICRO_SLACK_WEBHOOK=                         # Slack webhook URL (optional)

# ============================================================================
# INTEGRATION CONFIGURATION
# ============================================================================

# Redis/DragonflyDB
DRAGONFLY_URL=redis://dragonfly:6379
MICRO_LIGHTNING_REDIS_PREFIX=micro_lightning

# Prometheus
PROMETHEUS_URL=http://prometheus:9090
MICRO_LIGHTNING_METRICS_ENABLED=true

# Solana RPC
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_WS_URL=wss://api.mainnet-beta.solana.com

# Helius (for real-time data)
HELIUS_API_KEY=your_helius_api_key_here
HELIUS_WEBHOOK_URL=https://api.helius.xyz

# Jito (for MEV protection)
JITO_API_KEY=your_jito_api_key_here
JITO_ENDPOINT=https://mainnet.block-engine.jito.wtf

# TensorZero (for AI optimization)
TENSORZERO_API_KEY=your_tensorzero_api_key_here
TENSORZERO_ENDPOINT=http://tensorzero:3000

# ============================================================================
# DEVELOPMENT AND TESTING
# ============================================================================

# Development mode
MICRO_LIGHTNING_DEV_MODE=false
MICRO_LIGHTNING_SIMULATION_MODE=false
MICRO_LIGHTNING_PAPER_TRADING=true

# Testing configuration
MICRO_LIGHTNING_TEST_CAPITAL=20.0
MICRO_LIGHTNING_TEST_DURATION=3600           # Test duration in seconds (1 hour)
MICRO_LIGHTNING_TEST_TOKEN_COUNT=100         # Number of test tokens

# Logging
RUST_LOG=info,micro_lightning=debug
MICRO_LIGHTNING_LOG_LEVEL=info
MICRO_LIGHTNING_LOG_FILE=/app/logs/micro-lightning.log

# ============================================================================
# SECURITY CONFIGURATION
# ============================================================================

# Wallet security
MICRO_LIGHTNING_WALLET_ENCRYPTION=true
MICRO_LIGHTNING_KEY_ROTATION_INTERVAL=86400  # Key rotation interval (seconds)

# API security
MICRO_LIGHTNING_API_KEY=your_secure_api_key_here
MICRO_LIGHTNING_RATE_LIMIT=100               # API rate limit per minute

# Network security
MICRO_LIGHTNING_ALLOWED_IPS=127.0.0.1,::1    # Allowed IP addresses
MICRO_LIGHTNING_TLS_ENABLED=false            # TLS enabled for API

# ============================================================================
# OPERATIONAL LIMITS
# ============================================================================

# Daily limits
MICRO_DAILY_OPERATION_LIMIT=5                # Maximum operations per day
MICRO_DAILY_LOSS_LIMIT=4.0                   # Maximum daily loss ($)
MICRO_DAILY_PROFIT_TARGET=20.0               # Daily profit target ($)

# System limits
MICRO_MAX_MEMORY_USAGE=512                   # Maximum memory usage (MB)
MICRO_MAX_CPU_USAGE=80                       # Maximum CPU usage (%)
MICRO_MAX_DISK_USAGE=1024                    # Maximum disk usage (MB)

# Rate limits
MICRO_RPC_RATE_LIMIT=100                     # RPC requests per second
MICRO_API_RATE_LIMIT=50                      # API requests per minute
MICRO_WEBHOOK_RATE_LIMIT=10                  # Webhook calls per minute
